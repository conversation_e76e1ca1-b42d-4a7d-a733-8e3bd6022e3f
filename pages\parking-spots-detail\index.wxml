
<view class="parking-detail-container">
  <view class="parking-detail-content">
    <!-- 使用自定义渐变导航栏组件 -->
    <gradient-navbar
      title=""
      background="{{false}}"
      bind:back="onBackTap" 
    />

    <!-- 使用浮动头部组件 -->
    <floating-header enable-collapse="{{true}}" collapse-threshold="{{100}}">
      <!-- 主要内容（折叠时显示） -->
      <view slot="main" class="spot-name-container">
        <view class="spot-name-row">
          <view class="spot-name">{{spotInfo.name}}</view>
          <view wx:if="{{spotInfo.useStatus}}" class="status-tag {{spotInfo.useStatus.code === 'NO' ? 'status-free' : 'status-busy'}}">
            {{spotInfo.useStatus.code === 'NO' ? '空闲' : '使用中'}}
          </view>
        </view>
        <view wx:if="{{spotInfo.parkAddress || spotInfo.location}}" class="spot-location">
          {{spotInfo.parkAddress}}{{spotInfo.parkAddress && spotInfo.location ? ' - ' : ''}}{{spotInfo.location}}
        </view>
      </view>

      <!-- 次要内容（折叠时隐藏） -->
      <view slot="secondary">
        <view class="spot-info-row">
          <view class="info-item info-item-time">
            <view class="info-label">
              <t-icon name="time" size="40rpx" color="#ffffff" />
              <text>共享时段</text>
            </view>
            <view class="info-value">
              <text class="value-text">{{spotInfo.shareTime}}</text>
              <text wx:if="{{spotInfo.isCrossDay.code === 'YES'}}" class="cross-day-tag">跨天</text>
            </view>
          </view>

          <view class="info-divider"></view>

          <view class="info-item info-item-price">
            <view class="info-label">
              <t-icon name="money" size="40rpx" color="#ffffff" />
              <text>价格</text>
            </view>
            <view class="info-value">
              <text class="value-text">¥{{spotInfo.price}}/小时</text>
            </view>
          </view>
        </view>
      </view>
    </floating-header>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 车牌选择 -->
      <view class="form-item" bindtap="showPlateSelectorFun">
        <view class="form-label">车牌号码</view>
        <view class="form-value">
          <text class="{{selectedPlate ? '' : 'placeholder'}}">{{selectedPlate || '请选择车牌号'}}</text>
          <t-icon name="chevron-right" size="36rpx" color="#CCCCCC" />
        </view>
      </view>

      <!-- 保证金信息 -->
      <view class="form-item">
        <view class="form-label">
          保证金
          <view class="deposit-tip">多退少补</view>
        </view>
        <view class="form-value">
          <text class="{{depositPaid ? 'status-success' : 'status-warning'}}">{{depositPaid ? '已缴纳' : '¥99.00 (未缴纳)'}}</text>
        </view>
      </view>

      <!-- 停车时长 -->
      <view class="form-item">
        <view class="form-label">预计停车时长</view>
        <view class="form-value time-selector">
          <view class="time-control" bindtap="decreaseTime">
            <t-icon name="remove" size="36rpx" color="{{currentHourIndex > 0 ? '#0052D9' : '#CCCCCC'}}" />
          </view>
          <text class="time-value">{{estimatedHours}}小时</text>
          <view class="time-control" bindtap="increaseTime">
            <t-icon name="add" size="36rpx" color="{{currentHourIndex < availableHours.length - 1 ? '#0052D9' : '#CCCCCC'}}" />
          </view>
        </view>
      </view>

      <!-- 费用估算 -->
      <view class="form-item">
        <view class="form-label">
          预计费用
          <view wx:if="{{spotInfo.capAmount && spotInfo.capAmount !== '0.00'}}" class="cap-amount-tip">
            封顶 ¥{{spotInfo.capAmount}}
          </view>
        </view>
        <view class="form-value">
          <text class="fee-amount">¥{{estimatedFee}}</text>
        </view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="notice-section">
      <view class="notice-title">
        <t-icon name="info-circle" size="32rpx" color="#999999" />
        <text>温馨提示</text>
      </view>
      <view class="notice-content">
        <view class="notice-item">• 如车辆停放超出共享时间，影响业主使用，超出时间按4倍收费</view>
        <view class="notice-item">• 下单后即开始计算，出场后停止收费，多余保证金原路退回</view>
        <view class="notice-item" wx:if="{{spotInfo.isCrossDay.code === 'YES'}}">• 该车位共享时间跨天，请注意合理安排停车时间</view>
        <view class="notice-item" wx:if="{{spotInfo.useStatus.code === 'YES'}}">• 该车位当前正在使用中，无法预约</view>
      </view>
    </view>



  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="agreement-section">
      <t-checkbox
        checked="{{agreementChecked}}"
        bind:change="onAgreementChange"
        icon="circle"
        color="#0052D9"
        label=""
        borderless
      />
      <view class="agreement-text">
        阅读并同意
        <text class="agreement-link" bindtap="showAgreement" data-type="user">《用户协议》</text>、
        <text class="agreement-link" bindtap="showAgreement" data-type="manual">《用户手册》</text>
      </view>
    </view>

    <!-- 根据押金状态显示不同的按钮 -->
    <block wx:if="{{!depositPaid}}">
      <!-- 支付押金按钮 -->
      <t-button
        theme="success"
        size="large"
        block
        disabled="{{!agreementChecked}}"
        bindtap="onPayDeposit"
        class="order-button deposit-button"
      >支付押金 ¥99.00</t-button>
    </block>
    <block wx:else>
      <!-- 确认下单按钮 -->
      <t-button
        theme="primary"
        size="large"
        block
        disabled="{{!canOrder || !agreementChecked}}"
        bindtap="onOrderConfirm"
        class="order-button"
      >确认下单</t-button>
    </block>
  </view>
</view>



<!-- 车牌管理组件 -->
<plate-manager
  visible="{{showPlateSelector}}"
  selectedPlate="{{selectedPlate}}"
  bind:select="onPlateSelect"
  bind:delete="onPlateDelete"
  bind:close="closePlateSelector"
/>

<!-- 协议弹窗 -->
<t-dialog
  visible="{{showAgreementDialog}}"
  title="{{agreementTitle}}"
  content="{{agreementContent}}"
  confirm-btn="我已阅读"
  bind:confirm="closeAgreementDialog"
/>

<t-message id="t-message" />

<!-- 登录弹窗 -->
<login-overlay
  visible="{{loginOverlayVisible}}"
  bind:overlayclick="onLoginOverlayClick"
  bind:customevent="onLoginSuccess"
/>
